<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753962048992_vvfjy90gc" time="2025/07/31 19:40">
    <content>
      用户需要基于开发计划5编写AI小说助手的详细开发文档。项目是PySide6+Python桌面应用，包含大纲生成、章节编辑、人物管理、AI聊天等14个核心功能模块。要求Material UI设计风格，左右布局(40:60)，无假数据，支持多种AI模型(GPT/Claude/Gemini等)，内置提示词库和向量检索功能。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753962555061_zy4j687r0" time="2025/07/31 19:49">
    <content>
      已完成AI小说助手开发文档编写，包含完整的14个功能模块界面设计(ASCII绘图)、技术架构、开发路线图和部署方案。文档分为两部分，严格按照Material UI设计规范，左右布局(40:60)，无假数据，涵盖PySide6+Python技术栈，支持多AI模型集成，包含向量检索、降AI味等高级功能。
    </content>
    <tags>#其他</tags>
  </item>
</memory>