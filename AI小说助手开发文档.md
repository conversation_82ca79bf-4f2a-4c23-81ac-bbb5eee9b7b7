# AI小说助手开发文档

## 1. 项目概述

### 1.1 项目简介
AI小说助手是一款基于PySide6+Python开发的桌面应用程序，旨在帮助用户使用AI技术生成多章节的长篇、中长篇、短篇等网络小说，具备自动检索衔接上下文、伏笔等智能功能。

### 1.2 核心特性
- 支持多种AI模型（GPT、Claude、Gemini、自定义OpenAI、ModelScope、Ollama、SiliconFlow）
- 完整的小说创作流程（大纲生成→编辑→章节创作→分析润色）
- 智能上下文衔接和伏笔检索
- 内置依赖，免安装启动
- Material UI设计风格，明亮主题
- 向量库检索和提示词库管理

### 1.3 目标用户
- 网络小说作者
- 创意写作爱好者
- 内容创作者

## 2. 技术栈与架构

### 2.1 核心技术栈
```
前端框架：PySide6 (Qt6)
编程语言：Python 3.9+
UI设计：Material UI风格
数据存储：SQLite + JSON
向量检索：FAISS + Sentence-Transformers
打包工具：PyInstaller + Inno Setup
```

### 2.2 技术架构
```
┌─────────────────────────────────────────┐
│              用户界面层 (UI)              │
├─────────────────────────────────────────┤
│              业务逻辑层 (BLL)             │
├─────────────────────────────────────────┤
│              数据访问层 (DAL)             │
├─────────────────────────────────────────┤
│              AI服务层 (AI Service)        │
├─────────────────────────────────────────┤
│              数据存储层 (Storage)         │
└─────────────────────────────────────────┘
```

### 2.3 项目目录结构
```
AI小说助手/
├── main.py                    # 主程序入口
├── requirements.txt           # 依赖列表
├── config/                    # 配置文件
│   ├── __init__.py
│   ├── settings.py           # 应用设置
│   └── api_config.py         # API配置
├── ui/                       # 用户界面
│   ├── __init__.py
│   ├── main_window.py        # 主窗口
│   ├── components/           # UI组件
│   │   ├── __init__.py
│   │   ├── outline_generator.py    # 大纲生成
│   │   ├── outline_editor.py       # 大纲编辑
│   │   ├── chapter_editor.py       # 章节编辑
│   │   ├── chapter_generator.py    # 章节生成
│   │   ├── chapter_analyzer.py     # 章节分析
│   │   ├── character_editor.py     # 人物编辑
│   │   ├── relationship_graph.py   # 人物关系图
│   │   ├── statistics.py          # 统计信息
│   │   ├── ai_chat.py             # AI聊天
│   │   ├── prompt_library.py      # 提示词库
│   │   ├── context_manager.py     # 上下文管理
│   │   ├── vector_search.py       # 向量库检索
│   │   └── settings_panel.py      # 设置面板
│   └── styles/               # 样式文件
│       ├── __init__.py
│       ├── material_theme.py # Material主题
│       └── icons.py          # SVG图标
├── core/                     # 核心业务逻辑
│   ├── __init__.py
│   ├── novel_manager.py      # 小说管理器
│   ├── ai_service.py         # AI服务
│   ├── template_manager.py   # 模板管理
│   ├── character_manager.py  # 人物管理
│   ├── context_analyzer.py   # 上下文分析
│   └── vector_store.py       # 向量存储
├── data/                     # 数据层
│   ├── __init__.py
│   ├── database.py           # 数据库操作
│   ├── models.py             # 数据模型
│   └── storage.py            # 文件存储
├── utils/                    # 工具类
│   ├── __init__.py
│   ├── logger.py             # 日志工具
│   ├── file_handler.py       # 文件处理
│   ├── api_validator.py      # API验证
│   └── text_processor.py     # 文本处理
├── resources/                # 资源文件
│   ├── templates/            # 内置模板
│   ├── prompts/              # 内置提示词
│   └── icons/                # 图标资源
└── tests/                    # 测试文件
    ├── __init__.py
    └── test_*.py
```

## 3. 主界面设计

### 3.1 主界面布局 (ASCII图)
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│  AI小说助手 v1.0                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│ [新建项目] [打开项目] [保存项目] [导出文档] [系统设置] [使用帮助]                   │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────────┐ │
│ │   功能导航菜单    │ │                    主工作区域                          │ │
│ │                 │ │                                                         │ │
│ │ 📊 首页仪表盘    │ │                                                         │ │
│ │ 📝 大纲生成      │ │                                                         │ │
│ │ ✏️  大纲编辑      │ │                                                         │ │
│ │ 📖 章节编辑      │ │                                                         │ │
│ │ 🎯 章节生成      │ │                                                         │ │
│ │ 🔍 章节分析      │ │                                                         │ │
│ │ 👥 人物编辑      │ │                                                         │ │
│ │ 🕸️  人物关系图    │ │                                                         │ │
│ │ 📈 统计信息      │ │                                                         │ │
│ │ 💬 AI聊天对话   │ │                                                         │ │
│ │ 📚 提示词库      │ │                                                         │ │
│ │ 🧠 上下文管理    │ │                                                         │ │
│ │ 🔎 向量库检索    │ │                                                         │ │
│ │ ⚙️  系统设置      │ │                                                         │ │
│ └─────────────────┘ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 状态：就绪 | 当前项目：未命名 | 总字数：0 | 章节数：0 | AI模型：未配置 | 内存使用：正常 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 3.2 首页仪表盘界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                首页仪表盘                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │   项目概览       │ │   创作进度       │ │   AI使用统计     │ │   快速操作       │ │
│ │                 │ │                 │ │                 │ │                 │ │
│ │ 📖 小说标题      │ │ 📊 完成度       │ │ 🤖 调用次数     │ │ [新建项目]      │ │
│ │    [未设置]      │ │    [0%]         │ │    [0]          │ │                 │ │
│ │ 📚 小说类型      │ │ 📝 已完成章节   │ │ 💰 消耗Token    │ │ [打开项目]      │ │
│ │    [未设置]      │ │    [0章]        │ │    [0]          │ │                 │ │
│ │ 🎯 小说主题      │ │ 📄 总字数       │ │ ⏱️  平均响应时间 │ │ [生成大纲]      │ │
│ │    [未设置]      │ │    [0字]        │ │    [0秒]        │ │                 │ │
│ │ ✍️  写作风格      │ │ 📈 今日字数     │ │ 🔄 成功率       │ │ [开始创作]      │ │
│ │    [未设置]      │ │    [0字]        │ │    [0%]         │ │                 │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│                                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                              最近活动记录                                    │ │
│ │                                                                             │ │
│ │ • 暂无活动记录，开始您的创作之旅吧！                                         │ │
│ │                                                                             │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                              系统运行状态                                    │ │
│ │                                                                             │ │
│ │ � AI服务连接: 未配置 | 🟢 数据库状态: 正常 | 🟢 向量库状态: 就绪             │ │
│ │ 🟢 内存使用状态: 正常 | 🟢 磁盘空间: 充足 | 🟢 网络连接: 正常                │ │
│ │                                                                             │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 4. 核心功能模块

### 4.1 大纲生成模块

#### 4.1.1 功能描述
大纲生成模块是小说创作的起点，支持用户输入基本信息，选择AI模型，使用内置或自定义提示词模板生成完整的小说大纲。

#### 4.1.2 大纲生成界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  大纲生成                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         功能区 (40%)         │ │              生成区 (60%)                   │ │
│ │                             │ │                                             │ │
│ │ 🤖 AI模型选择               │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ┌─────────────────────────┐ │ │ │              大纲生成结果                │ │ │
│ │ │ [请选择AI模型] ▼        │ │ │ │                                         │ │ │
│ │ │ • OpenAI GPT            │ │ │ │                                         │ │ │
│ │ │ • Anthropic Claude      │ │ │ │                                         │ │ │
│ │ │ • Google Gemini         │ │ │ │                                         │ │ │
│ │ │ • ModelScope            │ │ │ │                                         │ │ │
│ │ │ • SiliconFlow           │ │ │ │                                         │ │ │
│ │ │ • 自定义OpenAI          │ │ │ │                                         │ │ │
│ │ │ • Ollama本地            │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 📝 提示词模板管理            │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [选择提示词模板] ▼      │ │ │ │                                         │ │ │
│ │ │ • 标准大纲生成模板      │ │ │ │                                         │ │ │
│ │ │ • 详细大纲生成模板      │ │ │ │                                         │ │ │
│ │ │ • 简化大纲生成模板      │ │ │ │                                         │ │ │
│ │ │ • 自定义模板...         │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │ [编辑模板] [新建模板]        │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 📖 小说基本信息              │ │ │                                         │ │ │
│ │ 小说标题:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [请输入小说标题]        │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 小说类型:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [请选择类型] ▼          │ │ │ │                                         │ │ │
│ │ │ • 玄幻                  │ │ │ │                                         │ │ │
│ │ │ • 都市                  │ │ │ │                                         │ │ │
│ │ │ • 历史                  │ │ │ │                                         │ │ │
│ │ │ • 科幻                  │ │ │ │                                         │ │ │
│ │ │ • 言情                  │ │ │ │                                         │ │ │
│ │ │ • 武侠                  │ │ │ │                                         │ │ │
│ │ │ • 悬疑                  │ │ │ │                                         │ │ │
│ │ │ • 其他...               │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 小说主题:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [请选择主题] ▼          │ │ │ │                                         │ │ │
│ │ │ • 成长                  │ │ │ │                                         │ │ │
│ │ │ • 爱情                  │ │ │ │                                         │ │ │
│ │ │ • 复仇                  │ │ │ │                                         │ │ │
│ │ │ • 冒险                  │ │ │ │                                         │ │ │
│ │ │ • 修仙                  │ │ │ │                                         │ │ │
│ │ │ • 重生                  │ │ │ │                                         │ │ │
│ │ │ • 穿越                  │ │ │ │                                         │ │ │
│ │ │ • 其他...               │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 写作风格:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [请选择风格] ▼          │ │ │ │                                         │ │ │
│ │ │ • 热血                  │ │ │ │                                         │ │ │
│ │ │ • 轻松                  │ │ │ │                                         │ │ │
│ │ │ • 幽默                  │ │ │ │                                         │ │ │
│ │ │ • 温馨                  │ │ │ │                                         │ │ │
│ │ │ • 悬疑                  │ │ │ │                                         │ │ │
│ │ │ • 浪漫                  │ │ │ │                                         │ │ │
│ │ │ • 严肃                  │ │ │ │                                         │ │ │
│ │ │ • 其他...               │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 📊 章节设置                  │ │ │                                         │ │ │
│ │ 总章节数:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [10] (范围: 1-9999)     │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 每章字数:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [3500] (范围: 300-9999) │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 👥 人物角色设置              │ │ │                                         │ │ │
│ │ 主角数量:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [1] (范围: 1-10)        │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 重要角色数量:               │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [3] (范围: 0-20)        │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 配角数量:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [5] (范围: 0-50)        │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 龙套数量:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [10] (范围: 0-100)      │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 🎯 生成范围设置              │ │ │                                         │ │ │
│ │ 起始章节:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [1]                     │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 结束章节:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [10]                    │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 🚀 操作按钮                 │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │      [生成大纲]         │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │      [清空内容]         │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │      [保存设置]         │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ └─────────────────────────────────────────┘ │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

#### 4.1.3 功能实现要点

**模型选择功能**
- 支持7种AI模型：GPT、Claude、Gemini、自定义OpenAI、ModelScope、Ollama、SiliconFlow
- 动态加载已配置的模型列表
- 实时显示模型连接状态

**提示词模板管理**
- 内置标准大纲提示词模板
- 支持用户自定义模板的增删改查
- 模板变量替换功能（如[用户输入的标题]）

**基本信息配置**
- 小说类型：玄幻、都市、历史、科幻、言情、武侠、悬疑、军事等
- 小说主题：修仙、重生、穿越、系统、商战、校园、宫斗等
- 小说风格：热血、轻松、黑暗、治愈、搞笑、严肃等

### 4.2 大纲编辑模块

#### 4.2.1 大纲编辑界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  大纲编辑                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         功能区 (40%)         │ │              编辑区 (60%)                   │ │
│ │                             │ │                                             │ │
│ │ 📖 小说标题编辑              │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ┌─────────────────────────┐ │ │ │              小说标题                    │ │ │
│ │ │ [请输入小说标题]        │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ [请在此输入或编辑小说标题]          │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │      [AI生成标题]       │ │ │ └─────────────────────────────────────────┘ │ │
│ │ └─────────────────────────┘ │ │                                             │ │
│ │                             │ │ ┌─────────────────────────────────────────┐ │ │
│ │ 🎯 中心思想编辑              │ │ │              中心思想                    │ │ │
│ │ ┌─────────────────────────┐ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │      [AI生成思想]       │ │ │ │ │                                     │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ [请在此输入或编辑小说的中心思想]    │ │ │ │
│ │                             │ │ │ │                                     │ │ │ │
│ │ 📝 故事梗概编辑              │ │ │ │                                     │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                     │ │ │ │
│ │ │      [AI生成梗概]       │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────┘ │ │ └─────────────────────────────────────────┘ │ │
│ │                             │ │                                             │ │
│ │ 🌍 世界观设定编辑            │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ┌─────────────────────────┐ │ │ │              故事梗概                    │ │ │
│ │ │      [AI生成世界观]     │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ └─────────────────────────┘ │ │ │ │                                     │ │ │ │
│ │                             │ │ │ │                                     │ │ │ │
│ │ 💾 文件操作                  │ │ │ │ [请在此输入或编辑故事梗概]          │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                     │ │ │ │
│ │ │      [保存修改]         │ │ │ │ │                                     │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │                                     │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                     │ │ │ │
│ │ │      [重置内容]         │ │ │ │ │                                     │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │                                     │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │      [导入大纲]         │ │ │ └─────────────────────────────────────────┘ │ │
│ │ └─────────────────────────┘ │ │                                             │ │
│ │ ┌─────────────────────────┐ │ │ ┌─────────────────────────────────────────┐ │ │
│ │ │      [导出大纲]         │ │ │ │              世界观设定                  │ │ │
│ │ └─────────────────────────┘ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │                             │ │ │ │                                     │ │ │ │
│ │ 🤖 AI辅助功能               │ │ │ │                                     │ │ │ │
│ │ AI模型选择:                 │ │ │ │ [请在此输入或编辑世界观设定]        │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                     │ │ │ │
│ │ │ [请选择AI模型] ▼        │ │ │ │ │                                     │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │                                     │ │ │ │
│ │                             │ │ │ │                                     │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                     │ │ │ │
│ │ │      [优化标题]         │ │ │ │ │                                     │ │ │ │
│ │ └─────────────────────────┘ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ ┌─────────────────────────┐ │ │ └─────────────────────────────────────────┘ │ │
│ │ │      [完善思想]         │ │ │                                             │ │
│ │ └─────────────────────────┘ │ │                                             │ │
│ │ ┌─────────────────────────┐ │ │                                             │ │
│ │ │      [扩展梗概]         │ │ │                                             │ │
│ │ └─────────────────────────┘ │ │                                             │ │
│ │ ┌─────────────────────────┐ │ │                                             │ │
│ │ │      [构建世界观]       │ │ │                                             │ │
│ │ └─────────────────────────┘ │ │                                             │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.3 章节编辑模块

#### 4.3.1 章节编辑界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  章节编辑                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         功能区 (40%)         │ │              编辑区 (60%)                   │ │
│ │                             │ │                                             │ │
│ │ 📚 章节列表管理              │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ┌─────────────────────────┐ │ │ │              章节标题编辑                │ │ │
│ │ │ 章节列表                │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │ ┌─────────────────────┐ │ │ │ │ │ [请输入章节标题]                    │ │ │ │
│ │ │ │ [选择章节] ▼        │ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │ │ • 第1章：[未设置]   │ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │ │ • 第2章：[未设置]   │ │ │ │ │ │      [AI生成标题]                   │ │ │ │
│ │ │ │ • 第3章：[未设置]   │ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │ │ • 第4章：[未设置]   │ │ │ │ └─────────────────────────────────────────┘ │ │
│ │ │ │ • 第5章：[未设置]   │ │ │ │                                             │ │
│ │ │ │ • ...               │ │ │ │ ┌─────────────────────────────────────────┐ │ │
│ │ │ └─────────────────────┘ │ │ │ │              章节摘要编辑                │ │ │
│ │ └─────────────────────────┘ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │                             │ │ │ │ │                                     │ │ │ │
│ │ 📝 章节管理操作              │ │ │ │ │                                     │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ │ [请输入章节摘要内容]                │ │ │ │
│ │ │      [添加章节]         │ │ │ │ │                                     │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ │                                     │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ │                                     │ │ │ │
│ │ │      [删除章节]         │ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ │      [AI生成摘要]                   │ │ │ │
│ │ │      [上移章节]         │ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────┘ │ │ │ └─────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────┐ │ │ │                                             │ │
│ │ │      [下移章节]         │ │ │ │ ┌─────────────────────────────────────────┐ │ │
│ │ └─────────────────────────┘ │ │ │ │              章节角色设定                │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                         │ │ │
│ │ │      [复制章节]         │ │ │ │ │ 当前章节涉及角色：                       │ │ │
│ │ └─────────────────────────┘ │ │ │ │                                         │ │ │
│ │                             │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ 👥 角色快速选择              │ │ │ │ │ ☐ [角色列表为空，请先创建角色]      │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │ 角色类型筛选：          │ │ │ │ │                                         │ │ │ │
│ │ │ ☐ 主角  ☐ 重要角色      │ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │ ☐ 配角  ☐ 反派          │ │ │ │ │ │      [选择角色]                     │ │ │ │
│ │ │ ☐ 龙套                  │ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │                             │ │ │ │ │      [添加新角色]                   │ │ │ │
│ │ 🤖 AI辅助功能               │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ AI模型选择:                 │ │ │ └─────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────┐ │ │ │                                             │ │
│ │ │ [请选择AI模型] ▼        │ │ │ │                                             │ │
│ │ └─────────────────────────┘ │ │ │                                             │ │
│ │                             │ │ │                                             │ │
│ │ ┌─────────────────────────┐ │ │ │                                             │ │
│ │ │      [生成章节摘要]     │ │ │ │                                             │ │
│ │ └─────────────────────────┘ │ │ │                                             │ │
│ │ ┌─────────────────────────┐ │ │ │                                             │ │
│ │ │      [优化章节标题]     │ │ │ │                                             │ │
│ │ └─────────────────────────┘ │ │ │                                             │ │
│ │ ┌─────────────────────────┐ │ │ │                                             │ │
│ │ │      [章节结构规划]     │ │ │ │                                             │ │
│ │ └─────────────────────────┘ │ │ │                                             │ │
│ │                             │ │ │                                             │ │
│ │ 💾 保存操作                  │ │ │                                             │ │
│ │ ┌─────────────────────────┐ │ │ │                                             │ │
│ │ │      [保存修改]         │ │ │ │                                             │ │
│ │ └─────────────────────────┘ │ │ │                                             │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.4 章节生成模块

#### 4.4.1 章节生成界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  章节生成                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         功能区 (40%)         │ │              生成区 (60%)                   │ │
│ │                             │ │                                             │ │
│ │ 📖 章节选择                  │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ┌─────────────────────────┐ │ │ │              章节基本信息                │ │ │
│ │ │ [请选择章节] ▼          │ │ │ │                                         │ │ │
│ │ │ • 第1章：[未设置]       │ │ │ │ 章节标题：[未选择章节]                   │ │ │
│ │ │ • 第2章：[未设置]       │ │ │ │ 目标字数：[0] 字                        │ │ │
│ │ │ • 第3章：[未设置]       │ │ │ │ 当前字数：[0] 字                        │ │ │
│ │ │ • 第4章：[未设置]       │ │ │ │ 完成进度：[0%]                          │ │ │
│ │ │ • 第5章：[未设置]       │ │ │ │ 创建时间：[未创建]                       │ │ │
│ │ │ • ...                   │ │ │ │ 更新时间：[未更新]                       │ │ │
│ │ └─────────────────────────┘ │ │ └─────────────────────────────────────────┘ │ │
│ │                             │ │                                             │ │
│ │ � 章节状态信息              │ │ ┌─────────────────────────────────────────┐ │ │
│ │ 当前状态: [未开始]          │ │ │              章节内容编辑区              │ │ │
│ │ 字数统计: [0/0]             │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ 完成度: [0%]                │ │ │ │                                     │ │ │ │
│ │ 最后保存: [从未保存]        │ │ │ │                                     │ │ │ │
│ │                             │ │ │ │                                     │ │ │ │
│ │ 👥 章节角色设置              │ │ │ │                                     │ │ │ │
│ │ 参与角色选择：              │ │ │ │ [请先选择章节，然后在此编辑内容]    │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                     │ │ │ │
│ │ │ ☐ [角色列表为空]        │ │ │ │ │                                     │ │ │ │
│ │ │ [请先创建角色]          │ │ │ │ │                                     │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │                                     │ │ │ │
│ │                             │ │ │ │                                     │ │ │ │
│ │ 🎯 生成参数设置              │ │ │ │                                     │ │ │ │
│ │ 目标字数:                   │ │ │ │                                     │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                     │ │ │ │
│ │ │ [3500] (300-9999)       │ │ │ │ │                                     │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │                                     │ │ │ │
│ │                             │ │ │ │                                     │ │ │ │
│ │ 写作风格:                   │ │ │ │                                     │ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                     │ │ │ │
│ │ │ [请选择风格] ▼          │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │ • 热血                  │ │ │ └─────────────────────────────────────────┘ │ │
│ │ │ • 轻松                  │ │ │                                             │ │
│ │ │ • 幽默                  │ │ │ ┌─────────────────────────────────────────┐ │ │
│ │ │ • 温馨                  │ │ │ │              操作控制区                  │ │ │
│ │ │ • 悬疑                  │ │ │ │                                         │ │ │
│ │ │ • 其他...               │ │ │ │ 文本选择操作：                           │ │ │
│ │ └─────────────────────────┘ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │                             │ │ │ │      [选择全部文本]                 │ │ │ │
│ │ 情节重点:                   │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │ [请选择重点] ▼          │ │ │ │ │      [复制选中文本]                 │ │ │ │
│ │ │ • 人物介绍              │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │ • 情节发展              │ │ │ │                                         │ │ │
│ │ │ • 场景描写              │ │ │ │ 内容生成操作：                           │ │ │
│ │ │ • 对话交流              │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │ • 动作描述              │ │ │ │ │      [生成章节内容]                 │ │ │ │
│ │ │ • 心理活动              │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │ • 其他...               │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ └─────────────────────────┘ │ │ │ │      [续写内容]                     │ │ │ │
│ │                             │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ 🤖 AI模型设置               │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │      [润色选中文本]                 │ │ │ │
│ │ │ [请选择AI模型] ▼        │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │                             │ │ │ │      [降AI味处理]                   │ │ │ │
│ │ 💾 文件操作                  │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │                                         │ │ │
│ │ │      [保存章节]         │ │ │ │ 文件操作：                               │ │ │
│ │ └─────────────────────────┘ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │      [导出为TXT]                    │ │ │ │
│ │ │      [导出章节]         │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │      [导出为DOCX]                   │ │ │ │
│ │ │      [重置内容]         │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────┘ │ │ └─────────────────────────────────────────┘ │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.5 章节分析模块

#### 4.5.1 章节分析界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  章节分析                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         功能区 (40%)         │ │              分析区 (60%)                   │ │
│ │                             │ │                                             │ │
│ │ 📖 章节选择                  │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ┌─────────────────────────┐ │ │ │              分析结果显示                │ │ │
│ │ │ [请选择要分析的章节] ▼  │ │ │ │                                         │ │ │
│ │ │ • 第1章：[未设置]       │ │ │ │                                         │ │ │
│ │ │ • 第2章：[未设置]       │ │ │ │                                         │ │ │
│ │ │ • 第3章：[未设置]       │ │ │ │                                         │ │ │
│ │ │ • 第4章：[未设置]       │ │ │ │                                         │ │ │
│ │ │ • 第5章：[未设置]       │ │ │ │                                         │ │ │
│ │ │ • ...                   │ │ │ │ [请先选择章节并设置分析选项]             │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 🔍 分析选项设置              │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ ☐ 核心剧情分析          │ │ │ │                                         │ │ │
│ │ │ ☐ 故事梗概提取          │ │ │ │                                         │ │ │
│ │ │ ☐ 优缺点分析            │ │ │ │                                         │ │ │
│ │ │ ☐ 角色行为标注          │ │ │ │                                         │ │ │
│ │ │ ☐ 重要物品标注          │ │ │ │                                         │ │ │
│ │ │ ☐ 改进建议生成          │ │ │ │                                         │ │ │
│ │ │ ☐ 情节连贯性检查        │ │ │ │                                         │ │ │
│ │ │ ☐ 文笔风格分析          │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │      [全选]             │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │      [全不选]           │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 🤖 AI分析设置               │ │ │                                         │ │ │
│ │ AI模型选择:                 │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [请选择AI模型] ▼        │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │                                         │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 分析深度:                   │ │ │                                         │ │ │
│ │ ┌─────────────────────────┐ │ │ │                                         │ │ │
│ │ │ [请选择分析深度] ▼      │ │ │ │                                         │ │ │
│ │ │ • 简要分析              │ │ │ │                                         │ │ │
│ │ │ • 标准分析              │ │ │ │                                         │ │ │
│ │ │ • 详细分析              │ │ │ │                                         │ │ │
│ │ │ • 深度分析              │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ └─────────────────────────────────────────┘ │ │
│ │                             │ │                                             │ │
│ │ 📊 分析操作                  │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ┌─────────────────────────┐ │ │ │              操作控制区                  │ │ │
│ │ │      [开始分析]         │ │ │ │                                         │ │ │
│ │ └─────────────────────────┘ │ │ │ 分析结果操作：                           │ │ │
│ │ ┌─────────────────────────┐ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │      [章节改进]         │ │ │ │ │      [复制分析结果]                 │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │      [导出分析报告]     │ │ │ │ │      [保存分析结果]                 │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │      [清空结果]         │ │ │ │ │      [导出为PDF]                    │ │ │ │
│ │ └─────────────────────────┘ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │                             │ │ │ │                                         │ │ │
│ │ 📈 分析历史记录              │ │ │ │ 章节改进操作：                           │ │ │
│ │ ┌─────────────────────────┐ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │ 历史分析记录：          │ │ │ │ │      [应用改进建议]                 │ │ │ │
│ │ │ [暂无分析记录]          │ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │                         │ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │                         │ │ │ │ │      [生成改进版本]                 │ │ │ │
│ │ │                         │ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ │                         │ │ │ │ │ ┌─────────────────────────────────────┐ │ │ │
│ │ │                         │ │ │ │ │      [对比原版本]                   │ │ │ │
│ │ │                         │ │ │ │ │ └─────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────┘ │ │ └─────────────────────────────────────────┘ │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.6 人物编辑模块

#### 4.6.1 界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  人物编辑                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         功能区 (40%)         │ │              编辑区 (60%)                   │ │
│ │                             │ │                                             │ │
│ │ 👥 角色列表                  │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ┌─────────────────────────┐ │ │ │              角色详情                    │ │ │
│ │ │ 🦸 主角                  │ │ │ │                                         │ │ │
│ │ │   • 李逍遥              │ │ │ │ 角色姓名: [___________________]         │ │ │
│ │ │ 👤 重要角色              │ │ │ │ 角色类型: [主角] ▼                      │ │ │
│ │ │   • 赵灵儿              │ │ │ │ 年龄: [18]                              │ │ │
│ │ │   • 林月如              │ │ │ │ 性别: [男] ▼                            │ │ │
│ │ │ 👥 配角                  │ │ │ │ 职业: [剑客]                            │ │ │
│ │ │   • 阿奴                │ │ │ │                                         │ │ │
│ │ │ 😈 反派                  │ │ │ │ 外貌描述:                               │ │ │
│ │ │   • 拜月教主            │ │ │ │ [_________________________________]     │ │ │
│ │ │ 👤 龙套                  │ │ │ │                                         │ │ │
│ │ │   • 客栈老板            │ │ │ │ 性格特点:                               │ │ │
│ │ └─────────────────────────┘ │ │ │ [_________________________________]     │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 📝 角色操作                  │ │ │ 背景故事:                               │ │ │
│ │ [添加角色]                  │ │ │ [_________________________________]     │ │ │
│ │ [编辑角色]                  │ │ │                                         │ │ │
│ │ [删除角色]                  │ │ │ 技能特长:                               │ │ │
│ │ [复制角色]                  │ │ │ [_________________________________]     │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 🤖 AI辅助                   │ │ │ 重要物品:                               │ │ │
│ │ 模型: [GPT-4] ▼             │ │ │ [_________________________________]     │ │ │
│ │ [AI生成角色]                │ │ └─────────────────────────────────────────┘ │ │
│ │ [完善设定]                  │ │                                             │ │
│ │ [生成头像]                  │ │ ┌─────────────────────────────────────────┐ │ │
│ │                             │ │ │              角色关系                    │ │ │
│ │ 💾 操作                      │ │ │ 与其他角色的关系:                       │ │ │
│ │ [保存修改]                  │ │ │ • 赵灵儿: 恋人                          │ │ │
│ │ [导入角色]                  │ │ │ • 林月如: 朋友                          │ │ │
│ │ [导出角色]                  │ │ │ [添加关系] [编辑关系]                   │ │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.7 人物关系图模块

#### 4.7.1 界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                人物关系图                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         功能区 (40%)         │ │              关系图区 (60%)                 │ │
│ │                             │ │                                             │ │
│ │ 🔗 关系管理                  │ │ ┌─────────────────────────────────────────┐ │ │
│ │                             │ │ │              关系图谱                    │ │ │
│ │ 角色1: [李逍遥] ▼           │ │ │                                         │ │ │
│ │ 角色2: [赵灵儿] ▼           │ │ │        李逍遥 ────恋人──── 赵灵儿        │ │ │
│ │ 关系: [恋人] ▼              │ │ │          │                  │            │ │ │
│ │                             │ │ │         朋友                师父          │ │ │
│ │ [添加关系]                  │ │ │          │                  │            │ │ │
│ │ [更新关系]                  │ │ │        林月如              酒剑仙         │ │ │
│ │ [删除关系]                  │ │ │          │                               │ │ │
│ │                             │ │ │         敌人                             │ │ │
│ │ 📊 关系统计                  │ │ │          │                               │ │ │
│ │ 总角色数: 8                 │ │ │       拜月教主                           │ │ │
│ │ 关系总数: 12                │ │ │                                         │ │ │
│ │ 主角关系: 5                 │ │ └─────────────────────────────────────────┘ │ │
│ │                             │ │                                             │ │
│ │ 🎨 显示设置                  │ │ ┌─────────────────────────────────────────┐ │ │
│ │ 布局方式: [力导向] ▼        │ │ │              关系列表                    │ │ │
│ │ 显示标签: ☑                 │ │ │ • 李逍遥 ←→ 赵灵儿 (恋人)               │ │ │
│ │ 显示头像: ☑                 │ │ │ • 李逍遥 ←→ 林月如 (朋友)               │ │ │
│ │ 自动布局: ☑                 │ │ │ • 李逍遥 ←→ 酒剑仙 (师父)               │ │ │
│ │                             │ │ │ • 李逍遥 ←→ 拜月教主 (敌人)             │ │ │
│ │ 🔍 筛选                      │ │ │ • 赵灵儿 ←→ 酒剑仙 (师父)               │ │ │
│ │ 角色类型: [全部] ▼          │ │ │ ...                                     │ │ │
│ │ 关系类型: [全部] ▼          │ │ └─────────────────────────────────────────┘ │ │
│ │                             │ │                                             │ │
│ │ 💾 操作                      │ │                                             │ │
│ │ [保存关系]                  │ │                                             │ │
│ │ [导出图片]                  │ │                                             │ │
│ │ [重置布局]                  │ │                                             │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.8 统计信息模块

#### 4.8.1 界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  统计信息                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                              概览统计                                        │ │
│ │ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │ │
│ │ │   小说信息       │ │   进度统计       │ │   字数统计       │ │   完成度     │ │ │
│ │ │                 │ │                 │ │                 │ │             │ │ │
│ │ │ 📖 仙剑奇侠传    │ │ 📊 章节数: 10   │ │ 📝 总字数: 35000│ │ 🎯 70%      │ │ │
│ │ │ 🎭 玄幻修仙      │ │ 📈 已完成: 7    │ │ 📄 平均字数: 3500│ │ ⏰ 预计3天  │ │ │
│ │ │ 🎨 热血风格      │ │ 📋 进行中: 1    │ │ 📊 最高字数: 4200│ │ 📅 今日: 0  │ │ │
│ │ │ 👥 角色数: 8     │ │ 📝 未开始: 2    │ │ 📉 最低字数: 2800│ │ 🔥 连续: 5天│ │ │
│ │ └─────────────────┘ └─────────────────┘ └─────────────────┘ └─────────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                              章节统计                                        │ │
│ │ ┌─────┬─────────────────────────┬─────────┬─────────┬─────────────────────┐ │ │
│ │ │章节号│        章节标题          │  字数   │  状态   │      最后更新        │ │ │
│ │ ├─────┼─────────────────────────┼─────────┼─────────┼─────────────────────┤ │ │
│ │ │  1  │ 第一章：初入江湖         │  3500   │ ✅ 完成 │ 2024-01-15 14:30   │ │ │
│ │ │  2  │ 第二章：奇遇仙人         │  3800   │ ✅ 完成 │ 2024-01-16 09:15   │ │ │
│ │ │  3  │ 第三章：习得剑法         │  3200   │ ✅ 完成 │ 2024-01-16 16:45   │ │ │
│ │ │  4  │ 第四章：初恋情缘         │  4200   │ ✅ 完成 │ 2024-01-17 11:20   │ │ │
│ │ │  5  │ 第五章：江湖恩怨         │  3600   │ ✅ 完成 │ 2024-01-17 19:30   │ │ │
│ │ │  6  │ 第六章：师父现身         │  3400   │ ✅ 完成 │ 2024-01-18 10:15   │ │ │
│ │ │  7  │ 第七章：修炼突破         │  3900   │ ✅ 完成 │ 2024-01-18 15:45   │ │ │
│ │ │  8  │ 第八章：危机四伏         │  2800   │ 🔄 进行中│ 2024-01-19 08:30   │ │ │
│ │ │  9  │ 第九章：生死一线         │   0     │ ⏳ 未开始│        -           │ │ │
│ │ │ 10  │ 第十章：大结局           │   0     │ ⏳ 未开始│        -           │ │ │
│ │ └─────┴─────────────────────────┴─────────┴─────────┴─────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                              操作区域                                        │ │
│ │ [刷新统计] [导出报告] [数据备份] [清理缓存]                                   │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.9 AI聊天模块

#### 4.9.1 界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  AI聊天                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                              聊天区域                                        │ │
│ │                                                                             │ │
│ │ 🤖 AI助手: 您好！我是您的AI写作助手，有什么可以帮助您的吗？                  │ │
│ │                                                                             │ │
│ │ 👤 用户: 我想为我的小说增加一些武功招式，有什么建议吗？                      │ │
│ │                                                                             │ │
│ │ 🤖 AI助手: 根据您的仙侠小说背景，我建议可以设计以下几类武功：                │ │
│ │ 1. 剑法类：如"御剑术"、"万剑归宗"                                           │ │
│ │ 2. 内功类：如"太极心法"、"九阳神功"                                         │ │
│ │ 3. 轻功类：如"凌波微步"、"踏雪无痕"                                         │ │
│ │ 您希望重点发展哪一类呢？                                                     │ │
│ │                                                                             │ │
│ │ 👤 用户: 剑法类比较感兴趣，能详细说说吗？                                    │ │
│ │                                                                             │ │
│ │ 🤖 AI助手: 好的！剑法类武功可以这样设计：                                    │ │
│ │ ...                                                                         │ │
│ │                                                                             │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                              输入区域                                        │ │
│ │ ┌─────────────────────────────────────────────────────────────────────────┐ │ │
│ │ │ 请输入您的问题...                                                        │ │ │
│ │ │                                                                         │ │ │
│ │ │                                                                         │ │ │
│ │ └─────────────────────────────────────────────────────────────────────────┘ │ │
│ │ 🤖 模型: [GPT-4] ▼  📝 [插入模板] ▼  🔄 [清空对话]  📤 [发送] (Ctrl+Enter) │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.10 提示词库模块

#### 4.10.1 界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  提示词库                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         分类区 (40%)         │ │              内容区 (60%)                   │ │
│ │                             │ │                                             │ │
│ │ 📚 内置提示词                │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ├─ 📝 大纲相关               │ │ │              提示词详情                  │ │ │
│ │ │  • 标准大纲生成            │ │ │                                         │ │ │
│ │ │  • 细纲扩展                │ │ │ 名称：标准大纲生成                       │ │ │
│ │ │  • 章节规划                │ │ │ 分类：大纲相关                          │ │ │
│ │ ├─ 📖 章节相关               │ │ │ 描述：用于生成完整小说大纲的标准模板     │ │ │
│ │ │  • 章节开头                │ │ │                                         │ │ │
│ │ │  • 章节结尾                │ │ │ 内容：                                   │ │ │
│ │ │  • 过渡章节                │ │ │ 请为我创建一部小说的详细大纲，具体要求如下：│ │ │
│ │ ├─ 🌍 世界观相关             │ │ │ 小说标题：[用户输入的标题]               │ │ │
│ │ │  • 世界设定                │ │ │ 小说类型：[用户输入的类型]               │ │ │
│ │ │  • 魔法体系                │ │ │ 主题：[用户输入的主题]                   │ │ │
│ │ │  • 势力分布                │ │ │ 风格：[用户输入的风格]                   │ │ │
│ │ ├─ 👥 人设相关               │ │ │ ...                                     │ │ │
│ │ │  • 主角设定                │ │ └─────────────────────────────────────────┘ │ │
│ │ │  • 配角设定                │ │                                             │ │
│ │ │  • 反派设定                │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ├─ ✍️  写作技巧               │ │ │              操作区域                    │ │ │
│ │ │  • 续写优化                │ │ │                                         │ │ │
│ │ │  • 扩写润色                │ │ │ [使用此模板] [复制内容] [编辑模板]       │ │ │
│ │ │  • 改写重构                │ │ │ [删除模板] [导出模板] [分享模板]         │ │ │
│ │ │  • 降AI味                  │ │ │                                         │ │ │
│ │ └─ 🎨 风格相关               │ │ │ 使用统计：                               │ │ │
│ │    • 古风写作                │ │ │ • 使用次数：156次                        │ │ │
│ │    • 现代都市                │ │ │ • 成功率：92%                           │ │ │
│ │    • 科幻未来                │ │ │ • 平均评分：4.8/5.0                     │ │ │
│ │                             │ │ └─────────────────────────────────────────┘ │ │
│ │ 👤 自定义提示词              │ │                                             │ │
│ │ ├─ 我的模板                  │ │                                             │ │
│ │ │  • 个人风格模板            │ │                                             │ │
│ │ │  • 专属角色模板            │ │                                             │ │
│ │ └─ 收藏模板                  │ │                                             │ │
│ │    • 收藏的优质模板          │ │                                             │ │
│ │                             │ │                                             │ │
│ │ 🔍 搜索                      │ │                                             │ │
│ │ [搜索提示词...]             │ │                                             │ │
│ │                             │ │                                             │ │
│ │ 📝 管理                      │ │                                             │ │
│ │ [新建模板]                  │ │                                             │ │
│ │ [导入模板]                  │ │                                             │ │
│ │ [批量管理]                  │ │                                             │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.11 上下文管理模块

#### 4.11.1 界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                上下文管理                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         管理区 (40%)         │ │              内容区 (60%)                   │ │
│ │                             │ │                                             │ │
│ │ 📚 上下文类型                │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ☑ 大纲信息                  │ │ │              上下文内容                  │ │ │
│ │ ☑ 人物设定                  │ │ │                                         │ │ │
│ │ ☑ 世界观设定                │ │ │ 【大纲信息】                             │ │ │
│ │ ☑ 前置章节                  │ │ │ 小说标题：仙剑奇侠传                     │ │ │
│ │ ☑ 人物关系                  │ │ │ 类型：玄幻修仙                          │ │ │
│ │ ☑ 重要物品                  │ │ │ 主题：成长与爱情                        │ │ │
│ │ ☑ 伏笔线索                  │ │ │                                         │ │ │
│ │                             │ │ │ 【人物设定】                             │ │ │
│ │ 🎯 章节范围                  │ │ │ 主角：李逍遥 - 18岁剑客，性格开朗...     │ │ │
│ │ 当前章节: 第8章             │ │ │ 女主：赵灵儿 - 女娲后人，温柔善良...     │ │ │
│ │ 参考范围: [1] 到 [7]        │ │ │                                         │ │ │
│ │                             │ │ │ 【世界观设定】                           │ │ │
│ │ 🔍 智能分析                  │ │ │ 修仙等级：练气→筑基→金丹→元婴...         │ │ │
│ │ [分析关联性]                │ │ │ 门派势力：蜀山派、峨眉派...             │ │ │
│ │ [检测矛盾]                  │ │ │                                         │ │ │
│ │ [提取伏笔]                  │ │ │ 【前置章节摘要】                         │ │ │
│ │ [生成摘要]                  │ │ │ 第1章：李逍遥初入江湖，遇到神秘老人...   │ │ │
│ │                             │ │ │ 第2章：获得仙剑，开始修炼之路...         │ │ │
│ │ 📊 上下文统计                │ │ │ ...                                     │ │ │
│ │ 总字符数: 15,420            │ │ │                                         │ │ │
│ │ Token估算: 3,855            │ │ │ 【重要伏笔】                             │ │ │
│ │ 关联度: 高                  │ │ │ • 神秘老人的真实身份                     │ │ │
│ │                             │ │ │ • 仙剑的来历和力量                       │ │ │
│ │ 🛠️ 操作                      │ │ │ • 赵灵儿的身世之谜                       │ │ │
│ │ [更新上下文]                │ │ └─────────────────────────────────────────┘ │ │
│ │ [优化内容]                  │ │                                             │ │
│ │ [导出上下文]                │ │                                             │ │
│ │ [重置设置]                  │ │                                             │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.12 向量库检索模块

#### 4.12.1 界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                向量库检索                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         检索区 (40%)         │ │              结果区 (60%)                   │ │
│ │                             │ │                                             │ │
│ │ 🔍 检索设置                  │ │ ┌─────────────────────────────────────────┐ │ │
│ │                             │ │ │              检索结果                    │ │ │
│ │ 检索内容:                   │ │ │                                         │ │ │
│ │ [剑法武功相关内容...]       │ │ │ 相关度: 0.95                            │ │ │
│ │                             │ │ │ 来源: 第3章 - 习得剑法                   │ │ │
│ │ 🎯 检索范围                  │ │ │ 内容: "李逍遥在山洞中发现了一本古老的剑谱，│ │ │
│ │ ☑ 章节内容                  │ │ │ 名为《御剑术》，这是一门高深的剑法..."   │ │ │
│ │ ☑ 人物设定                  │ │ │                                         │ │ │
│ │ ☑ 世界观设定                │ │ │ ─────────────────────────────────────── │ │ │
│ │ ☑ 大纲信息                  │ │ │                                         │ │ │
│ │ ☐ 对话记录                  │ │ │ 相关度: 0.87                            │ │ │
│ │                             │ │ │ 来源: 人物设定 - 酒剑仙                  │ │ │
│ │ 📊 检索参数                  │ │ │ 内容: "酒剑仙是蜀山派的长老，精通各种剑法，│ │ │
│ │ 相似度阈值: [0.7] ▼         │ │ │ 尤其擅长酒后剑法，威力无穷..."           │ │ │
│ │ 返回数量: [10] ▼            │ │ │                                         │ │ │
│ │ 检索模式: [语义] ▼          │ │ │ ─────────────────────────────────────── │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 🤖 嵌入模型                  │ │ │ 相关度: 0.82                            │ │ │
│ │ 模型: [text-embedding] ▼    │ │ │ 来源: 第5章 - 江湖恩怨                   │ │ │
│ │ API密钥: [已配置] ✅        │ │ │ 内容: "江湖中流传着一套失传已久的剑法，   │ │ │
│ │ [测试连接]                  │ │ │ 据说练成后可以御剑飞行..."               │ │ │
│ │                             │ │ │                                         │ │ │
│ │ 📈 向量库状态                │ │ │ ─────────────────────────────────────── │ │ │
│ │ 总文档数: 1,245             │ │ │                                         │ │ │
│ │ 向量维度: 1536              │ │ │ 相关度: 0.78                            │ │ │
│ │ 索引大小: 15.2MB            │ │ │ 来源: 世界观设定 - 武功体系              │ │ │
│ │ 最后更新: 2024-01-19        │ │ │ 内容: "修仙界的武功分为天地玄黄四个等级，│ │ │
│ │                             │ │ │ 剑法类武功以灵活多变著称..."             │ │ │
│ │ 🛠️ 操作                      │ │ └─────────────────────────────────────────┘ │ │
│ │ [开始检索]                  │ │                                             │ │
│ │ [重建索引]                  │ │ ┌─────────────────────────────────────────┐ │ │
│ │ [清空缓存]                  │ │ │              操作区域                    │ │ │
│ │ [导出结果]                  │ │ │ [插入到章节] [保存结果] [复制内容]       │ │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 4.13 设置模块

#### 4.13.1 界面布局
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                    设置                                         │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────────────────────────┐ │
│ │         设置分类 (30%)       │ │              设置内容 (70%)                 │ │
│ │                             │ │                                             │ │
│ │ 🤖 AI模型设置               │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ├─ OpenAI (GPT)             │ │ │              OpenAI设置                  │ │ │
│ │ ├─ Anthropic (Claude)       │ │ │                                         │ │ │
│ │ ├─ Google (Gemini)          │ │ │ API密钥:                                 │ │ │
│ │ ├─ ModelScope               │ │ │ [sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx]   │ │ │
│ │ ├─ SiliconFlow              │ │ │                                         │ │ │
│ │ ├─ 自定义OpenAI              │ │ │ 模型名称:                               │ │ │
│ │ └─ Ollama本地               │ │ │ [gpt-4-turbo] ▼                        │ │ │
│ │                             │ │ │ • gpt-4-turbo                          │ │ │
│ │ 🎨 界面设置                  │ │ │ • gpt-4o                               │ │ │
│ │ ├─ 主题颜色                  │ │ │ • gpt-3.5-turbo                        │ │ │
│ │ ├─ 字体设置                  │ │ │ • 自定义模型                            │ │ │
│ │ ├─ 布局设置                  │ │ │                                         │ │ │
│ │ └─ 窗口设置                  │ │ │ 自定义模型名称:                         │ │ │
│ │                             │ │ │ [_________________________________]     │ │ │
│ │ 📝 编辑器设置                │ │ │                                         │ │ │
│ │ ├─ 字体大小                  │ │ │ API地址:                                │ │ │
│ │ ├─ 行间距                    │ │ │ [https://api.openai.com/v1]            │ │ │
│ │ ├─ 自动保存                  │ │ │                                         │ │ │
│ │ └─ 语法高亮                  │ │ │ 连接超时 (秒):                          │ │ │
│ │                             │ │ │ [30] ▼                                  │ │ │
│ │ 💾 数据设置                  │ │ │                                         │ │ │
│ │ ├─ 自动备份                  │ │ │ 最大Token数:                            │ │ │
│ │ ├─ 数据导入导出              │ │ │ [4096] ▼                               │ │ │
│ │ ├─ 缓存管理                  │ │ │                                         │ │ │
│ │ └─ 隐私设置                  │ │ │ 温度参数:                               │ │ │
│ │                             │ │ │ [0.7] (0.0-2.0)                        │ │ │
│ │ 🔧 高级设置                  │ │ │                                         │ │ │
│ │ ├─ 性能优化                  │ │ │ [测试连接] [保存设置] [重置默认]         │ │ │
│ │ ├─ 调试模式                  │ │ │                                         │ │ │
│ │ ├─ 日志设置                  │ │ │ 连接状态: 🟢 已连接                      │ │ │
│ │ └─ 更新检查                  │ │ │ 最后测试: 2024-01-19 10:30              │ │ │
│ │                             │ │ └─────────────────────────────────────────┘ │ │
│ └─────────────────────────────┘ └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 5. 技术实现细节

### 5.1 核心类设计

#### 5.1.1 主窗口类 (MainWindow)
```python
class MainWindow(QMainWindow):
    """主窗口类，负责整体界面布局和功能模块管理"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_components()
        self.load_settings()

    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle("AI小说助手 v1.0")
        self.setMinimumSize(1200, 800)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(central_widget)

        # 创建左侧导航菜单
        self.navigation_menu = NavigationMenu()
        main_layout.addWidget(self.navigation_menu, 0)

        # 创建右侧工作区
        self.work_area = QStackedWidget()
        main_layout.addWidget(self.work_area, 1)

        # 设置布局比例
        main_layout.setStretch(0, 2)  # 导航菜单占20%
        main_layout.setStretch(1, 8)  # 工作区占80%
```

#### 5.1.2 AI服务类 (AIService)
```python
class AIService:
    """AI服务类，统一管理各种AI模型的调用"""

    def __init__(self):
        self.models = {}
        self.current_model = None
        self.load_models()

    def load_models(self):
        """加载所有配置的AI模型"""
        # 加载OpenAI模型
        if self.is_openai_configured():
            self.models['openai'] = OpenAIModel()

        # 加载Claude模型
        if self.is_claude_configured():
            self.models['claude'] = ClaudeModel()

        # 加载其他模型...

    async def generate_text(self, prompt: str, model_name: str = None) -> str:
        """生成文本内容"""
        model = self.get_model(model_name)
        if not model:
            raise ValueError(f"模型 {model_name} 未配置或不可用")

        try:
            response = await model.generate(prompt)
            return response
        except Exception as e:
            logger.error(f"AI生成失败: {e}")
            raise
```

#### 5.1.3 小说管理器类 (NovelManager)
```python
class NovelManager:
    """小说管理器，负责小说项目的创建、保存、加载等操作"""

    def __init__(self):
        self.current_novel = None
        self.database = Database()

    def create_novel(self, title: str, genre: str, theme: str, style: str) -> Novel:
        """创建新的小说项目"""
        novel = Novel(
            title=title,
            genre=genre,
            theme=theme,
            style=style,
            created_at=datetime.now()
        )

        # 保存到数据库
        novel_id = self.database.save_novel(novel)
        novel.id = novel_id

        self.current_novel = novel
        return novel

    def save_novel(self, novel: Novel) -> bool:
        """保存小说项目"""
        try:
            self.database.update_novel(novel)
            return True
        except Exception as e:
            logger.error(f"保存小说失败: {e}")
            return False
```

### 5.2 数据模型设计

#### 5.2.1 小说模型 (Novel)
```python
@dataclass
class Novel:
    """小说数据模型"""
    id: Optional[int] = None
    title: str = ""
    genre: str = ""
    theme: str = ""
    style: str = ""
    outline: str = ""
    world_setting: str = ""
    chapters: List['Chapter'] = field(default_factory=list)
    characters: List['Character'] = field(default_factory=list)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def get_total_words(self) -> int:
        """获取总字数"""
        return sum(chapter.word_count for chapter in self.chapters)

    def get_completion_rate(self) -> float:
        """获取完成度"""
        if not self.chapters:
            return 0.0
        completed = sum(1 for chapter in self.chapters if chapter.is_completed)
        return completed / len(self.chapters)
```

#### 5.2.2 章节模型 (Chapter)
```python
@dataclass
class Chapter:
    """章节数据模型"""
    id: Optional[int] = None
    novel_id: int = 0
    chapter_number: int = 0
    title: str = ""
    summary: str = ""
    content: str = ""
    word_count: int = 0
    target_words: int = 3500
    is_completed: bool = False
    characters: List[str] = field(default_factory=list)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def update_word_count(self):
        """更新字数统计"""
        self.word_count = len(self.content.replace(' ', '').replace('\n', ''))
```

#### 5.2.3 角色模型 (Character)
```python
@dataclass
class Character:
    """角色数据模型"""
    id: Optional[int] = None
    novel_id: int = 0
    name: str = ""
    character_type: str = "配角"  # 主角、重要角色、配角、反派、龙套
    age: int = 0
    gender: str = ""
    occupation: str = ""
    appearance: str = ""
    personality: str = ""
    background: str = ""
    skills: str = ""
    items: str = ""
    relationships: Dict[str, str] = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
```

### 5.3 数据库设计

#### 5.3.1 数据库表结构
```sql
-- 小说表
CREATE TABLE novels (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    genre TEXT,
    theme TEXT,
    style TEXT,
    outline TEXT,
    world_setting TEXT,
    chapter_count INTEGER DEFAULT 0,
    target_words_per_chapter INTEGER DEFAULT 3500,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 章节表
CREATE TABLE chapters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    novel_id INTEGER NOT NULL,
    chapter_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    summary TEXT,
    content TEXT,
    word_count INTEGER DEFAULT 0,
    target_words INTEGER DEFAULT 3500,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE
);

-- 角色表
CREATE TABLE characters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    novel_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    character_type TEXT DEFAULT '配角',
    age INTEGER,
    gender TEXT,
    occupation TEXT,
    appearance TEXT,
    personality TEXT,
    background TEXT,
    skills TEXT,
    items TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE
);

-- 角色关系表
CREATE TABLE character_relationships (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    novel_id INTEGER NOT NULL,
    character1_id INTEGER NOT NULL,
    character2_id INTEGER NOT NULL,
    relationship_type TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels (id) ON DELETE CASCADE,
    FOREIGN KEY (character1_id) REFERENCES characters (id) ON DELETE CASCADE,
    FOREIGN KEY (character2_id) REFERENCES characters (id) ON DELETE CASCADE
);

-- 提示词模板表
CREATE TABLE prompt_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    is_builtin BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 设置表
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE NOT NULL,
    value TEXT,
    category TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5.4 AI模型集成

#### 5.4.1 OpenAI集成
```python
class OpenAIModel:
    """OpenAI模型集成类"""

    def __init__(self, api_key: str, model_name: str = "gpt-4-turbo"):
        self.client = OpenAI(api_key=api_key)
        self.model_name = model_name

    async def generate(self, prompt: str, max_tokens: int = 4096, temperature: float = 0.7) -> str:
        """生成文本内容"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise
```

#### 5.4.2 Claude集成
```python
class ClaudeModel:
    """Claude模型集成类"""

    def __init__(self, api_key: str, model_name: str = "claude-3-opus-20240229"):
        self.client = anthropic.Anthropic(api_key=api_key)
        self.model_name = model_name

    async def generate(self, prompt: str, max_tokens: int = 4096, temperature: float = 0.7) -> str:
        """生成文本内容"""
        try:
            response = await self.client.messages.create(
                model=self.model_name,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        except Exception as e:
            logger.error(f"Claude API调用失败: {e}")
            raise
```

#### 5.4.3 API验证器
```python
class APIValidator:
    """API连接验证器"""

    @staticmethod
    async def validate_openai(api_key: str, model_name: str, api_url: str = None) -> bool:
        """验证OpenAI API连接"""
        try:
            # 智能检测API地址
            if api_url and not api_url.endswith('/v1'):
                if not api_url.endswith('/'):
                    api_url += '/'
                api_url += 'v1'

            client = OpenAI(api_key=api_key, base_url=api_url)

            # 发送测试请求
            response = await client.chat.completions.create(
                model=model_name,
                messages=[{"role": "user", "content": "测试连接"}],
                max_tokens=10
            )
            return True
        except Exception as e:
            logger.error(f"OpenAI API验证失败: {e}")
            return False

    @staticmethod
    async def validate_claude(api_key: str, model_name: str) -> bool:
        """验证Claude API连接"""
        try:
            client = anthropic.Anthropic(api_key=api_key)
            response = await client.messages.create(
                model=model_name,
                max_tokens=10,
                messages=[{"role": "user", "content": "测试连接"}]
            )
            return True
        except Exception as e:
            logger.error(f"Claude API验证失败: {e}")
            return False
```

### 5.5 向量库实现

#### 5.5.1 向量存储类
```python
class VectorStore:
    """向量存储和检索类"""

    def __init__(self, embedding_model: str = "text-embedding-ada-002"):
        self.embedding_model = embedding_model
        self.index = None
        self.documents = []
        self.embeddings = []

    def add_document(self, content: str, metadata: dict):
        """添加文档到向量库"""
        # 生成嵌入向量
        embedding = self.get_embedding(content)

        # 添加到存储
        self.documents.append({
            'content': content,
            'metadata': metadata,
            'embedding': embedding
        })

        # 更新FAISS索引
        self.update_index()

    def get_embedding(self, text: str) -> List[float]:
        """获取文本的嵌入向量"""
        try:
            response = openai.Embedding.create(
                model=self.embedding_model,
                input=text
            )
            return response['data'][0]['embedding']
        except Exception as e:
            logger.error(f"获取嵌入向量失败: {e}")
            raise

    def search(self, query: str, top_k: int = 10, threshold: float = 0.7) -> List[dict]:
        """搜索相关文档"""
        if not self.index:
            return []

        # 获取查询向量
        query_embedding = self.get_embedding(query)

        # 执行搜索
        scores, indices = self.index.search(
            np.array([query_embedding]).astype('float32'),
            top_k
        )

        # 过滤结果
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if score >= threshold:
                results.append({
                    'content': self.documents[idx]['content'],
                    'metadata': self.documents[idx]['metadata'],
                    'score': float(score)
                })

        return results
```

### 5.6 上下文管理

#### 5.6.1 上下文分析器
```python
class ContextAnalyzer:
    """上下文分析器，负责分析和提取小说的上下文信息"""

    def __init__(self, novel: Novel):
        self.novel = novel

    def extract_context(self, current_chapter: int, context_range: int = 3) -> dict:
        """提取指定章节的上下文信息"""
        context = {
            'outline': self.novel.outline,
            'world_setting': self.novel.world_setting,
            'characters': self._get_relevant_characters(current_chapter),
            'previous_chapters': self._get_previous_chapters(current_chapter, context_range),
            'plot_threads': self._extract_plot_threads(current_chapter),
            'foreshadowing': self._extract_foreshadowing(current_chapter)
        }
        return context

    def _get_relevant_characters(self, chapter_num: int) -> List[Character]:
        """获取相关角色信息"""
        # 获取在当前章节及之前章节出现的角色
        relevant_chars = []
        for char in self.novel.characters:
            if self._character_appears_before(char, chapter_num):
                relevant_chars.append(char)
        return relevant_chars

    def _extract_plot_threads(self, chapter_num: int) -> List[str]:
        """提取剧情线索"""
        plot_threads = []

        # 分析前面章节的内容，提取未完结的剧情线
        for chapter in self.novel.chapters[:chapter_num-1]:
            threads = self._analyze_chapter_threads(chapter)
            plot_threads.extend(threads)

        return list(set(plot_threads))  # 去重

    def _extract_foreshadowing(self, chapter_num: int) -> List[str]:
        """提取伏笔信息"""
        foreshadowing = []

        # 使用AI分析前面章节中的伏笔
        for chapter in self.novel.chapters[:chapter_num-1]:
            foreshadows = self._analyze_foreshadowing(chapter)
            foreshadowing.extend(foreshadows)

        return foreshadowing
```

### 5.7 降AI味功能

#### 5.7.1 AI味检测器
```python
class AIFlavorDetector:
    """AI味检测器，识别和优化AI生成内容的机械化特征"""

    def __init__(self):
        self.ai_patterns = [
            r'首先.*其次.*最后',  # 典型的AI结构化表达
            r'值得注意的是',      # AI常用过渡词
            r'总的来说',          # AI总结性表达
            r'需要指出的是',      # AI强调性表达
            r'不可否认的是',      # AI客观性表达
        ]

    def detect_ai_flavor(self, text: str) -> dict:
        """检测文本中的AI味"""
        issues = []
        score = 0

        # 检测典型AI模式
        for pattern in self.ai_patterns:
            matches = re.findall(pattern, text)
            if matches:
                issues.append({
                    'type': 'ai_pattern',
                    'pattern': pattern,
                    'matches': matches,
                    'severity': 'medium'
                })
                score += len(matches) * 10

        # 检测句式单调
        sentences = self._split_sentences(text)
        if self._check_monotonous_structure(sentences):
            issues.append({
                'type': 'monotonous_structure',
                'description': '句式结构过于单调',
                'severity': 'low'
            })
            score += 5

        # 检测情感表达不足
        if self._check_emotion_lack(text):
            issues.append({
                'type': 'emotion_lack',
                'description': '情感表达不够丰富',
                'severity': 'medium'
            })
            score += 15

        return {
            'score': min(score, 100),  # 最高100分
            'level': self._get_ai_level(score),
            'issues': issues
        }

    def optimize_text(self, text: str) -> str:
        """优化文本，降低AI味"""
        optimized = text

        # 替换典型AI表达
        replacements = {
            r'首先(.*?)其次(.*?)最后': r'\1，\2，\3',
            r'值得注意的是': '',
            r'总的来说': '总之',
            r'需要指出的是': '',
            r'不可否认的是': '显然',
        }

        for pattern, replacement in replacements.items():
            optimized = re.sub(pattern, replacement, optimized)

        # 增加情感色彩
        optimized = self._add_emotional_elements(optimized)

        # 丰富句式结构
        optimized = self._diversify_sentence_structure(optimized)

        return optimized
```

## 6. 开发路线图

### 6.1 第一阶段：基础框架搭建 (2周)
1. **项目初始化**
   - 创建项目目录结构
   - 配置开发环境
   - 设置版本控制

2. **核心框架开发**
   - 主窗口框架
   - 导航菜单系统
   - 基础UI组件库

3. **数据层实现**
   - SQLite数据库设计
   - 数据模型定义
   - 基础CRUD操作

### 6.2 第二阶段：核心功能开发 (4周)
1. **大纲生成模块**
   - AI模型集成
   - 提示词模板系统
   - 大纲生成界面

2. **章节管理模块**
   - 章节编辑器
   - 章节生成功能
   - 内容保存机制

3. **人物管理模块**
   - 角色编辑器
   - 人物关系图
   - 角色数据管理

### 6.3 第三阶段：高级功能开发 (3周)
1. **AI聊天功能**
   - 对话界面
   - 多模型支持
   - 聊天记录管理

2. **向量库检索**
   - 嵌入模型集成
   - FAISS索引构建
   - 语义搜索功能

3. **上下文管理**
   - 上下文分析器
   - 智能关联检测
   - 伏笔提取功能

### 6.4 第四阶段：优化和完善 (2周)
1. **性能优化**
   - 界面响应优化
   - 内存使用优化
   - 数据库查询优化

2. **用户体验优化**
   - 界面美化
   - 交互优化
   - 错误处理完善

3. **测试和调试**
   - 功能测试
   - 性能测试
   - Bug修复

### 6.5 第五阶段：打包发布 (1周)
1. **应用打包**
   - PyInstaller配置
   - 依赖库打包
   - 安装程序制作

2. **文档编写**
   - 用户手册
   - 安装指南
   - 常见问题解答

3. **发布准备**
   - 版本号管理
   - 更新日志
   - 发布测试

## 7. 部署配置

### 7.1 开发环境配置

#### 7.1.1 Python环境要求
```bash
# Python版本要求
Python 3.9+

# 虚拟环境创建
python -m venv ai_novel_assistant
source ai_novel_assistant/bin/activate  # Linux/Mac
ai_novel_assistant\Scripts\activate     # Windows
```

#### 7.1.2 依赖包安装
```bash
# 核心依赖
pip install PySide6==6.6.0
pip install SQLAlchemy==2.0.23
pip install aiohttp==3.9.1
pip install openai==1.6.1
pip install anthropic==0.8.1
pip install google-generativeai==0.3.2

# 向量检索依赖
pip install faiss-cpu==1.7.4
pip install sentence-transformers==2.2.2
pip install numpy==1.24.3

# 工具库依赖
pip install requests==2.31.0
pip install python-dotenv==1.0.0
pip install loguru==0.7.2
pip install pydantic==2.5.2
pip install jinja2==3.1.2

# 开发工具依赖
pip install pytest==7.4.3
pip install black==23.11.0
pip install flake8==6.1.0
pip install mypy==1.7.1
```

#### 7.1.3 配置文件模板
```ini
# config/settings.ini
[DEFAULT]
app_name = AI小说助手
version = 1.0.0
debug = false

[DATABASE]
db_path = data/novels.db
backup_interval = 3600

[UI]
theme = material_light
font_family = Microsoft YaHei
font_size = 12
window_width = 1200
window_height = 800

[AI_MODELS]
default_model = openai
max_tokens = 4096
temperature = 0.7
timeout = 30

[VECTOR_STORE]
embedding_model = text-embedding-ada-002
index_path = data/vector_index
dimension = 1536
```

### 7.2 打包配置

#### 7.2.1 PyInstaller配置文件
```python
# build.spec
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('resources', 'resources'),
        ('config', 'config'),
        ('data/templates', 'data/templates'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets',
        'PySide6.QtGui',
        'sqlite3',
        'openai',
        'anthropic',
        'faiss',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='AI小说助手',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icons/app.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='AI小说助手',
)
```

#### 7.2.2 Inno Setup安装脚本
```ini
; setup.iss
[Setup]
AppName=AI小说助手
AppVersion=1.0.0
AppPublisher=AI小说助手开发团队
DefaultDirName={autopf}\AI小说助手
DefaultGroupName=AI小说助手
OutputDir=dist
OutputBaseFilename=AI小说助手_v1.0.0_Setup
Compression=lzma
SolidCompression=yes
WizardStyle=modern
SetupIconFile=resources\icons\app.ico
UninstallDisplayIcon={app}\AI小说助手.exe

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "创建桌面快捷方式"; GroupDescription: "附加图标:"
Name: "quicklaunchicon"; Description: "创建快速启动栏快捷方式"; GroupDescription: "附加图标:"

[Files]
Source: "dist\AI小说助手\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\AI小说助手"; Filename: "{app}\AI小说助手.exe"
Name: "{group}\卸载AI小说助手"; Filename: "{uninstallexe}"
Name: "{autodesktop}\AI小说助手"; Filename: "{app}\AI小说助手.exe"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\AI小说助手"; Filename: "{app}\AI小说助手.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\AI小说助手.exe"; Description: "启动AI小说助手"; Flags: nowait postinstall skipifsilent
```

### 7.3 Material UI主题实现

#### 7.3.1 主题配置
```python
# ui/styles/material_theme.py
class MaterialTheme:
    """Material UI主题配置"""

    # 主色调
    PRIMARY_COLOR = "#2196F3"      # 蓝色
    PRIMARY_LIGHT = "#64B5F6"      # 浅蓝色
    PRIMARY_DARK = "#1976D2"       # 深蓝色

    # 辅助色调
    SECONDARY_COLOR = "#FF9800"    # 橙色
    SECONDARY_LIGHT = "#FFB74D"    # 浅橙色
    SECONDARY_DARK = "#F57C00"     # 深橙色

    # 状态色调
    SUCCESS_COLOR = "#4CAF50"      # 绿色
    WARNING_COLOR = "#FF9800"      # 橙色
    ERROR_COLOR = "#F44336"        # 红色
    INFO_COLOR = "#2196F3"         # 蓝色

    # 中性色调
    BACKGROUND_COLOR = "#FAFAFA"   # 背景色
    SURFACE_COLOR = "#FFFFFF"      # 表面色
    TEXT_PRIMARY = "#212121"       # 主要文本
    TEXT_SECONDARY = "#757575"     # 次要文本
    DIVIDER_COLOR = "#E0E0E0"      # 分割线

    @classmethod
    def get_stylesheet(cls) -> str:
        """获取完整的样式表"""
        return f"""
        /* 主窗口样式 */
        QMainWindow {{
            background-color: {cls.BACKGROUND_COLOR};
            color: {cls.TEXT_PRIMARY};
            font-family: 'Microsoft YaHei', sans-serif;
        }}

        /* 按钮样式 */
        QPushButton {{
            background-color: {cls.PRIMARY_COLOR};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
        }}

        QPushButton:hover {{
            background-color: {cls.PRIMARY_LIGHT};
        }}

        QPushButton:pressed {{
            background-color: {cls.PRIMARY_DARK};
        }}

        QPushButton:disabled {{
            background-color: #BDBDBD;
            color: #9E9E9E;
        }}

        /* 成功按钮 */
        QPushButton[class="success"] {{
            background-color: {cls.SUCCESS_COLOR};
        }}

        /* 警告按钮 */
        QPushButton[class="warning"] {{
            background-color: {cls.WARNING_COLOR};
        }}

        /* 错误按钮 */
        QPushButton[class="error"] {{
            background-color: {cls.ERROR_COLOR};
        }}

        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {cls.SURFACE_COLOR};
            border: 1px solid {cls.DIVIDER_COLOR};
            border-radius: 4px;
            padding: 8px;
            font-size: 14px;
            color: {cls.TEXT_PRIMARY};
        }}

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border: 2px solid {cls.PRIMARY_COLOR};
        }}

        /* 下拉框样式 */
        QComboBox {{
            background-color: {cls.SURFACE_COLOR};
            border: 1px solid {cls.DIVIDER_COLOR};
            border-radius: 4px;
            padding: 8px;
            font-size: 14px;
            color: {cls.TEXT_PRIMARY};
        }}

        /* 列表样式 */
        QListWidget {{
            background-color: {cls.SURFACE_COLOR};
            border: 1px solid {cls.DIVIDER_COLOR};
            border-radius: 4px;
            alternate-background-color: #F5F5F5;
        }}

        QListWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {cls.DIVIDER_COLOR};
        }}

        QListWidget::item:selected {{
            background-color: {cls.PRIMARY_LIGHT};
            color: white;
        }}

        /* 标签页样式 */
        QTabWidget::pane {{
            border: 1px solid {cls.DIVIDER_COLOR};
            background-color: {cls.SURFACE_COLOR};
        }}

        QTabBar::tab {{
            background-color: #F5F5F5;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}

        QTabBar::tab:selected {{
            background-color: {cls.PRIMARY_COLOR};
            color: white;
        }}

        /* 进度条样式 */
        QProgressBar {{
            border: 1px solid {cls.DIVIDER_COLOR};
            border-radius: 4px;
            text-align: center;
            background-color: #F5F5F5;
        }}

        QProgressBar::chunk {{
            background-color: {cls.PRIMARY_COLOR};
            border-radius: 3px;
        }}

        /* 状态栏样式 */
        QStatusBar {{
            background-color: {cls.SURFACE_COLOR};
            border-top: 1px solid {cls.DIVIDER_COLOR};
            color: {cls.TEXT_SECONDARY};
        }}
        """
```

## 8. 附录

### 8.1 内置小说类型列表
```python
NOVEL_GENRES = [
    "玄幻", "修仙", "都市", "历史", "军事", "科幻", "游戏", "体育",
    "言情", "武侠", "仙侠", "奇幻", "悬疑", "灵异", "二次元", "轻小说",
    "现实", "青春", "古代言情", "现代言情", "幻想言情", "浪漫青春",
    "商战", "官场", "农村", "职场", "校园", "娱乐圈", "重生", "穿越",
    "系统", "无限流", "末世", "星际", "机甲", "异能", "血族", "魔法"
]
```

### 8.2 内置小说主题列表
```python
NOVEL_THEMES = [
    "成长", "爱情", "友情", "亲情", "复仇", "救赎", "探索", "冒险",
    "权力", "正义", "自由", "梦想", "命运", "选择", "牺牲", "背叛",
    "重生", "穿越", "修仙", "升级", "争霸", "建设", "经营", "收集",
    "推理", "解谜", "悬疑", "恐怖", "治愈", "温馨", "搞笑", "日常",
    "战争", "和平", "科技", "魔法", "异世界", "平行世界", "时空", "轮回"
]
```

### 8.3 内置小说风格列表
```python
NOVEL_STYLES = [
    "热血", "轻松", "幽默", "搞笑", "温馨", "治愈", "励志", "感人",
    "悬疑", "紧张", "刺激", "惊悚", "恐怖", "黑暗", "沉重", "深刻",
    "浪漫", "甜蜜", "虐心", "催泪", "感动", "温暖", "清新", "文艺",
    "严肃", "正经", "庄重", "古典", "现代", "时尚", "前卫", "创新",
    "传统", "经典", "怀旧", "复古", "简约", "华丽", "精致", "粗犷"
]
```

### 8.4 内置提示词模板示例

#### 8.4.1 大纲生成模板
```
模板名称：标准大纲生成
模板分类：大纲
模板描述：用于生成完整小说大纲的标准模板

模板内容：
请为我创建一部小说的详细大纲，具体要求如下：

小说标题：[用户输入的标题]
小说类型：[用户输入的类型]
主题：[用户输入的主题]
风格：[用户输入的风格]

章节数：[用户设置的章节数]章
每章字数：[用户设置的字数]字

人物设置：
主角数量：[用户设置的主角数量]个
重要角色数量：[用户设置的重要角色数量]个
配角数量：[用户设置的配角数量]个
龙套数量：[用户设置的龙套数量]个

生成范围：从第[起始章]章 到 第[结束章]章

请生成以下内容：
1. 小说标题
2. 核心主题
3. 主要人物（包括姓名、身份、性格特点和背景故事）
4. 故事梗概
5. 章节结构（每章包含标题、简介和具体章节）
6. 世界观设定

特别要求：
1. 章节标题必须包含章节号，如"第一章：章节标题"
2. 只生成指定范围内的章节，但保持与已有大纲的一致性
3. 确保大纲结构完整、逻辑合理
4. 以JSON格式返回结果

请确保内容符合网络小说读者的喜好，适合在起点、番茄小说等平台发布。
```

#### 8.4.2 章节续写模板
```
模板名称：章节续写优化
模板分类：章节
模板描述：用于续写和优化章节内容

模板内容：
请根据以下信息续写小说章节：

【小说信息】
标题：[小说标题]
类型：[小说类型]
风格：[小说风格]

【当前章节】
章节号：第[章节号]章
章节标题：[章节标题]
目标字数：[目标字数]字

【已有内容】
[已有章节内容]

【上下文信息】
[相关上下文]

【续写要求】
1. 保持与前文的连贯性和一致性
2. 符合人物性格设定
3. 推进主要剧情发展
4. 保持[小说风格]的写作风格
5. 字数控制在[目标字数]字左右
6. 避免AI味过重，增加真实感和情感色彩

请续写章节内容，确保情节自然流畅，人物对话生动，场景描写细腻。
```

### 8.5 常见问题解答

#### 8.5.1 API配置问题
**Q: 如何配置OpenAI API？**
A: 在设置页面选择OpenAI，输入您的API密钥和模型名称，点击测试连接确认配置正确。

**Q: 支持哪些AI模型？**
A: 支持GPT系列、Claude系列、Gemini系列、ModelScope、SiliconFlow、自定义OpenAI兼容API和Ollama本地模型。

#### 8.5.2 功能使用问题
**Q: 如何生成连贯的章节内容？**
A: 系统会自动分析前面章节的内容，提取人物关系、剧情线索和伏笔，确保生成的内容与前文保持连贯。

**Q: 什么是降AI味功能？**
A: 降AI味功能可以检测和优化AI生成内容中的机械化表达，使文本更加自然和人性化。

#### 8.5.3 技术支持
**Q: 应用无法启动怎么办？**
A: 请检查系统要求，确保安装了必要的运行库，查看日志文件获取详细错误信息。

**Q: 如何备份我的小说项目？**
A: 使用文件菜单中的导出功能，或者直接备份data目录下的数据库文件。

---

**文档版本**: v1.0.0
**最后更新**: 2024年1月19日
**文档状态**: 完整版

本文档涵盖了AI小说助手的完整开发规范，包括功能设计、技术实现、界面布局、数据结构等所有关键信息。开发团队应严格按照此文档进行开发，确保产品质量和用户体验。
